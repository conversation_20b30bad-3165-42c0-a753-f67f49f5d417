# MouseInputHandler Setup Guide

## Overview
The MouseInputHandler provides a centralized system for handling mouse interactions with UI elements using Unity's new Input System. It raycasts against UI elements and triggers events that other scripts can subscribe to.

## Features
- **OnMouseEnter(GameObject)**: Triggered when the mouse first enters a UI element
- **OnMouseLeave(GameObject)**: Triggered when the mouse leaves a UI element  
- **OnMouseClick(GameObject)**: Triggered when the user clicks on a UI element

## Setup Instructions

### 1. Prerequisites
- Unity's new Input System package must be installed
- An EventSystem with InputSystemUIInputModule must be present in the scene
- A Canvas with GraphicRaycaster component must be present in the scene

### 2. Input Actions Setup
1. Open your Input Actions asset (InputSystem_Actions.inputactions)
2. Create or ensure you have these actions in the "Player" action map:
   - **Mouse Position**: 
     - Action Type: Value
     - Control Type: Vector2
     - Binding: Mouse > Position
   - **Mouse Click**:
     - Action Type: Button  
     - Control Type: Button
     - Binding: Mouse > Left Button

### 3. Component Setup
1. Add the MouseInputHandler component to any GameObject in your scene
2. In the Inspector, assign the Input Action References:
   - **Mouse Position Action**: Drag the Mouse Position action from your Input Actions asset
   - **Mouse Click Action**: Drag the Mouse Click action from your Input Actions asset
3. Configure the settings:
   - **UI Layer Mask**: Set which layers should be raycastable (default: all layers)
   - **UI Camera**: Assign the camera used for UI rendering (auto-detected if not set)

### 4. Using the Events in Your Scripts
```csharp
public class YourScript : MonoBehaviour
{
    private void OnEnable()
    {
        MouseInputHandler.OnMouseEnter += HandleMouseEnter;
        MouseInputHandler.OnMouseLeave += HandleMouseLeave;
        MouseInputHandler.OnMouseClick += HandleMouseClick;
    }
    
    private void OnDisable()
    {
        MouseInputHandler.OnMouseEnter -= HandleMouseEnter;
        MouseInputHandler.OnMouseLeave -= HandleMouseLeave;
        MouseInputHandler.OnMouseClick -= HandleMouseClick;
    }
    
    private void HandleMouseEnter(GameObject hitObject)
    {
        Debug.Log($"Mouse entered: {hitObject.name}");
    }
    
    private void HandleMouseLeave(GameObject hitObject)
    {
        Debug.Log($"Mouse left: {hitObject.name}");
    }
    
    private void HandleMouseClick(GameObject hitObject)
    {
        Debug.Log($"Mouse clicked: {hitObject.name}");
    }
}
```

## Fallback Mode
If Input Actions are not properly configured, the MouseInputHandler will automatically fall back to using Mouse.current from the Input System for basic functionality.

## Troubleshooting
- **No events firing**: Check that EventSystem and GraphicRaycaster are present in scene
- **Input Actions not working**: Verify Input Action References are assigned in Inspector
- **Wrong objects detected**: Adjust the UI Layer Mask setting
- **Performance issues**: Consider limiting raycast frequency if needed

## Example Usage
See MouseInputExample.cs for a complete example of how to use the events to create interactive UI effects.
