{"name": "InputSystem_Actions", "maps": [{"name": "Game", "id": "df70fa95-8a34-4494-b137-73ab6b9c7d37", "actions": [{"name": "MouseMove", "type": "Value", "id": "e5a64553-f8be-456f-b6df-920d1a9abc3a", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "MouseClick", "type": "<PERSON><PERSON>", "id": "7efcb372-3796-44b8-8d79-f3678675e524", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "", "id": "3df8553d-fa20-4ef3-890c-b9c3fd6c3dbb", "path": "<Mouse>/position", "interactions": "", "processors": "", "groups": "", "action": "MouseMove", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "25ea354a-a385-4f5f-8ccf-38956df11834", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": "", "action": "MouseClick", "isComposite": false, "isPartOfComposite": false}]}, {"name": "UI", "id": "28a34c94-fa88-4ce6-ab38-1a2b01813cfc", "actions": [], "bindings": []}], "controlSchemes": [{"name": "Keyboard&Mouse", "bindingGroup": "Keyboard&Mouse", "devices": [{"devicePath": "<Keyboard>", "isOptional": false, "isOR": false}, {"devicePath": "<Mouse>", "isOptional": false, "isOR": false}]}, {"name": "Gamepad", "bindingGroup": "Gamepad", "devices": [{"devicePath": "<Gamepad>", "isOptional": false, "isOR": false}]}, {"name": "Touch", "bindingGroup": "Touch", "devices": [{"devicePath": "<Touchscreen>", "isOptional": false, "isOR": false}]}, {"name": "Joystick", "bindingGroup": "Joystick", "devices": [{"devicePath": "<Joystick>", "isOptional": false, "isOR": false}]}, {"name": "XR", "bindingGroup": "XR", "devices": [{"devicePath": "<XRController>", "isOptional": false, "isOR": false}]}]}