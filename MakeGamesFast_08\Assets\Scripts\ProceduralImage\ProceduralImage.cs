using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;


[AddComponentMenu("UI/Procedural Image")]
[RequireComponent(typeof(RectTransform), typeof(CanvasRenderer))]
public class ProceduralImage : MaskableGraphic
{
    [Header("Shape")]
    [SerializeField][UnityEngine.Min(0)] private float _cornerRadius = 0.0f;

    [Header("Stroke")]
    [SerializeField] private StrokeLocation _strokeLocation = StrokeLocation.Center;
    [SerializeField][UnityEngine.Min(0)] private float _strokeWidth = 0.0f;
    [SerializeField] private Color _strokeColor = Color.black;

    [Header("Image")]
    private Texture2D _image = null;

    // Properties with validation
    public float cornerRadius
    {
        get => _cornerRadius;
        set
        {
            _cornerRadius = Mathf.Max(0f, value);
            SetVerticesDirty();
        }
    }

    public StrokeLocation strokeLocation
    {
        get => _strokeLocation;
        set
        {
            _strokeLocation = value;
            SetVerticesDirty();
        }
    }

    public float strokeWidth
    {
        get => _strokeWidth;
        set
        {
            _strokeWidth = Mathf.Max(0f, value);
            SetVerticesDirty();
        }
    }

    public Color strokeColor
    {
        get => _strokeColor;
        set
        {
            _strokeColor = value;
            SetVerticesDirty();
        }
    }

    public Texture2D image
    {
        get => _image;
        set
        {
            _image = value;
            SetMaterialDirty();
        }
    }

    public override Texture mainTexture
    {
        get
        {
            if (_image != null)
                return _image;
            if (material != null && material.mainTexture != null)
                return material.mainTexture;
            return s_WhiteTexture;
        }
    }
    protected override void Awake()
    {
        base.Awake();

        // Ensure we have a default material
        if (material == null)
            material = defaultMaterial;
    }

    protected override void Start()
    {
        base.Start();

        // Force initial mesh generation
        SetVerticesDirty();
    }

    protected override void Reset()
    {
        base.Reset();

        // Set default values when component is first added
        _cornerRadius = 0f;
        _strokeLocation = StrokeLocation.Center;
        _strokeWidth = 0f;
        _strokeColor = Color.black;
        _image = null;

        // Ensure we have proper material
        if (material == null)
            material = defaultMaterial;
    }

    protected override void OnValidate()
    {
        base.OnValidate();

        // Force mesh update
        SetVerticesDirty();
    }

    protected override void OnRectTransformDimensionsChange()
    {
        base.OnRectTransformDimensionsChange();

    }


    protected override void OnPopulateMesh(VertexHelper vh)
    {
        vh.Clear();

        Rect rect = GetPixelAdjustedRect();
        RectBounds shapeBounds = new RectBounds(rect);

        // Ensure corner radius doesn't exceed half the minimum dimension
        float maxRadius = Mathf.Min(shapeBounds.width, shapeBounds.height) * 0.5f;
        float actualCornerRadius = Mathf.Min(_cornerRadius, maxRadius);


        GenerateFillMesh(vh, shapeBounds, actualCornerRadius, color);

        // Generate stroke mesh
        if (_strokeWidth > 0 && _strokeColor.a > 0)
        {
            GenerateStrokeMesh(vh, shapeBounds, actualCornerRadius, _strokeWidth, _strokeColor, _strokeLocation);
        }

        // Generate image mesh (if different from fill texture and stroke doesn't fill shape)
        if (_image != null && color.a > 0)
        {
            GenerateFillMesh(vh, shapeBounds, actualCornerRadius, color);
        }
    }

    private void GenerateFillMesh(VertexHelper vh, RectBounds bounds, float cornerRadius, Color color)
    {
        if (cornerRadius <= 0)
        {
            // Simple rectangle
            AddQuad(vh, bounds.bottomLeft, bounds.topRight, color, Vector2.zero, Vector2.one);
        }
        else
        {
            // Rounded rectangle
            GenerateRoundedRectangle(vh, bounds, cornerRadius, color, Vector2.zero, Vector2.one);
        }
    }


    private void GenerateStrokeMesh(VertexHelper vh, RectBounds shapeBounds, float cornerRadius, float strokeWidth, Color strokeColor, StrokeLocation location)
    {
        // 0. When stroke width is 0 => generate no mesh
        if (strokeWidth <= 0) return;

        // Calculate inner and outer stroke widths
        float innerStrokeWidth = GetInnerStrokeWidth(strokeWidth, location);
        float outerStrokeWidth = GetOuterStrokeWidth(strokeWidth, location);

        // Calculate shape dimensions
        float minShapeDimension = Mathf.Min(shapeBounds.width, shapeBounds.height);

        // Calculate outer and inner rectangles
        RectBounds outerBounds = shapeBounds.Expand(outerStrokeWidth);
        RectBounds innerBounds = shapeBounds.Contract(innerStrokeWidth);

        // Calculate corner radii
        float outerCornerRadius = cornerRadius + outerStrokeWidth;
        float innerCornerRadius = cornerRadius - innerStrokeWidth;

        // 3. When inner stroke width >= half of minimum dimension => no interior, generate as fill
        if (innerStrokeWidth >= minShapeDimension * 0.5f)
        {
            GenerateFillMesh(vh, outerBounds, outerCornerRadius, strokeColor);
        }
        // 2. When inner stroke width >= corner radius (with safety margin) => use simplified inner shape
        // BUT: If corner radius is at maximum (perfect rounded rectangle), prefer normal stroke when possible
        else if (innerStrokeWidth >= cornerRadius)
        {
            GenerateStrokeWithSimplifiedInner(vh, outerBounds, innerBounds, outerCornerRadius, strokeColor);
        }
        // 1. When inner stroke width < corner radius (with safety margin) OR when we should prefer normal stroke => use normal stroke generation
        else
        {
            GenerateNormalStroke(vh, outerBounds, innerBounds, outerCornerRadius, innerCornerRadius, strokeColor);
        }
    }


    private float GetInnerStrokeWidth(float strokeWidth, StrokeLocation location)
    {
        switch (location)
        {
            case StrokeLocation.Outside: return 0f;
            case StrokeLocation.Inside: return strokeWidth;
            case StrokeLocation.Center: return strokeWidth * 0.5f;
            default: return 0f;
        }
    }

    private float GetOuterStrokeWidth(float strokeWidth, StrokeLocation location)
    {
        switch (location)
        {
            case StrokeLocation.Outside: return strokeWidth;
            case StrokeLocation.Inside: return 0f;
            case StrokeLocation.Center: return strokeWidth * 0.5f;
            default: return 0f;
        }
    }

    // Case 2: Generate stroke with simplified inner shape (4 vertices)
    private void GenerateStrokeWithSimplifiedInner(VertexHelper vh, RectBounds outerBounds, RectBounds innerBounds, float outerCornerRadius, Color strokeColor)
    {
        int segments = Mathf.Max(4, Mathf.RoundToInt(outerCornerRadius * 0.5f));

        // Generate outer corner vertices for each corner
        List<List<Vector2>> cornerVertices = new List<List<Vector2>>();

        // Corner centers for outer shape
        Vector2[] outerCornerCenters = {
            new Vector2(outerBounds.left + outerCornerRadius, outerBounds.bottom + outerCornerRadius), // Bottom-left
            new Vector2(outerBounds.right - outerCornerRadius, outerBounds.bottom + outerCornerRadius), // Bottom-right
            new Vector2(outerBounds.right - outerCornerRadius, outerBounds.top - outerCornerRadius), // Top-right
            new Vector2(outerBounds.left + outerCornerRadius, outerBounds.top - outerCornerRadius)  // Top-left
        };

        // Starting angles for each corner
        float[] startAngles = {
            Mathf.PI,        // Bottom-left: start from left (180°)
            3f * Mathf.PI / 2f, // Bottom-right: start from bottom (270°)
            0f,              // Top-right: start from right (0°)
            Mathf.PI / 2f    // Top-left: start from top (90°)
        };

        // Generate vertices for each corner arc
        for (int corner = 0; corner < 4; corner++)
        {
            List<Vector2> cornerArc = new List<Vector2>();
            float startAngle = startAngles[corner];

            for (int i = 0; i <= segments; i++)
            {
                float angle = startAngle + (i * Mathf.PI * 0.5f / segments);
                Vector2 offset = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * outerCornerRadius;
                cornerArc.Add(outerCornerCenters[corner] + offset);
            }
            cornerVertices.Add(cornerArc);
        }

        // Inner vertices (4 corners only)
        Vector2[] innerVertices = {
            innerBounds.bottomLeft,  // Bottom-left
            innerBounds.bottomRight, // Bottom-right
            innerBounds.topRight,    // Top-right
            innerBounds.topLeft      // Top-left
        };

        // Generate stroke as 4 quarter-circles + 4 connecting rectangles
        GenerateQuarterCircleStroke(vh, cornerVertices, innerVertices, strokeColor);
    }

    // Case 1: Generate normal stroke (both inner and outer have rounded corners)
    private void GenerateNormalStroke(VertexHelper vh, RectBounds outerBounds, RectBounds innerBounds, float outerCornerRadius, float innerCornerRadius, Color strokeColor)
    {
        // Clamp radii to valid ranges
        outerCornerRadius = Mathf.Clamp(outerCornerRadius, 0f, Mathf.Min(outerBounds.width, outerBounds.height) * 0.5f);
        innerCornerRadius = Mathf.Clamp(innerCornerRadius, 0f, Mathf.Min(innerBounds.width, innerBounds.height) * 0.5f);

        if (outerCornerRadius <= 0 && innerCornerRadius <= 0)
        {
            // Simple rectangle stroke
            GenerateRectangleStroke(vh, outerBounds, innerBounds, strokeColor);
        }
        else
        {
            // Rounded rectangle stroke with additional validation
            GenerateValidatedRoundedRectangleStroke(vh, outerBounds, innerBounds, outerCornerRadius, innerCornerRadius, strokeColor);
        }
    }

    private void GenerateValidatedRoundedRectangleStroke(VertexHelper vh, RectBounds outerBounds, RectBounds innerBounds, float outerRadius, float innerRadius, Color strokeColor)
    {
        // Generate vertices with validation
        int segments = Mathf.Max(4, Mathf.RoundToInt(Mathf.Max(outerRadius, innerRadius) * 0.5f));

        List<Vector2> outerVertices = GenerateRoundedRectangleVertices(outerBounds, outerRadius, segments);
        List<Vector2> innerVertices = GenerateRoundedRectangleVertices(innerBounds, innerRadius, segments);

        // Validate that we have matching vertex counts
        if (outerVertices.Count != innerVertices.Count)
        {
            // Fallback to simplified approach if vertex counts don't match
            Debug.Log("vertex count it not matching");
            return;
        }

        // Generate the stroke mesh
        int vertexCount = outerVertices.Count;
        int startIndex = vh.currentVertCount;

        // Add all vertices
        for (int i = 0; i < vertexCount; i++)
        {
            vh.AddVert(new Vector3(outerVertices[i].x, outerVertices[i].y, 0), strokeColor, Vector2.zero);
            vh.AddVert(new Vector3(innerVertices[i].x, innerVertices[i].y, 0), strokeColor, Vector2.zero);
        }

        // Add triangles to form the stroke
        for (int i = 0; i < vertexCount; i++)
        {
            int next = (i + 1) % vertexCount;

            int outerCurrent = startIndex + i * 2;
            int innerCurrent = startIndex + i * 2 + 1;
            int outerNext = startIndex + next * 2;
            int innerNext = startIndex + next * 2 + 1;

            // Create quad between current and next vertex pairs
            vh.AddTriangle(outerCurrent, outerNext, innerCurrent);
            vh.AddTriangle(innerCurrent, outerNext, innerNext);
        }
    }

    private void GenerateQuarterCircleStroke(VertexHelper vh, List<List<Vector2>> cornerVertices, Vector2[] innerVertices, Color strokeColor)
    {
        // Generate 4 quarter-circle strokes (one for each corner)
        for (int corner = 0; corner < 4; corner++)
        {
            GenerateCornerStroke(vh, cornerVertices[corner], innerVertices[corner], strokeColor);
        }

        // Generate 4 connecting rectangles between corners
        for (int side = 0; side < 4; side++)
        {
            int nextSide = (side + 1) % 4;
            GenerateSideStroke(vh, cornerVertices[side], cornerVertices[nextSide], innerVertices[side], innerVertices[nextSide], strokeColor);
        }
    }

    private void GenerateCornerStroke(VertexHelper vh, List<Vector2> outerCornerVertices, Vector2 innerCornerVertex, Color strokeColor)
    {
        int startIndex = vh.currentVertCount;

        // Add inner corner vertex
        vh.AddVert(new Vector3(innerCornerVertex.x, innerCornerVertex.y, 0), strokeColor, Vector2.zero);

        // Add all outer corner vertices
        foreach (var vertex in outerCornerVertices)
        {
            vh.AddVert(new Vector3(vertex.x, vertex.y, 0), strokeColor, Vector2.zero);
        }

        // Create triangle fan from inner vertex to outer arc
        for (int i = 0; i < outerCornerVertices.Count - 1; i++)
        {
            vh.AddTriangle(startIndex, startIndex + 1 + i, startIndex + 1 + i + 1);
        }
    }

    private void GenerateSideStroke(VertexHelper vh, List<Vector2> corner1Vertices, List<Vector2> corner2Vertices, Vector2 inner1, Vector2 inner2, Color strokeColor)
    {
        // Get the connecting vertices between corners
        Vector2 outer1 = corner1Vertices[corner1Vertices.Count - 1]; // Last vertex of first corner
        Vector2 outer2 = corner2Vertices[0]; // First vertex of second corner

        // Create rectangle between the two corners
        int startIndex = vh.currentVertCount;

        vh.AddVert(new Vector3(inner1.x, inner1.y, 0), strokeColor, Vector2.zero);     // 0
        vh.AddVert(new Vector3(inner2.x, inner2.y, 0), strokeColor, Vector2.zero);     // 1
        vh.AddVert(new Vector3(outer2.x, outer2.y, 0), strokeColor, Vector2.zero);     // 2
        vh.AddVert(new Vector3(outer1.x, outer1.y, 0), strokeColor, Vector2.zero);     // 3

        // Create two triangles for the rectangle
        vh.AddTriangle(startIndex, startIndex + 1, startIndex + 2);
        vh.AddTriangle(startIndex, startIndex + 2, startIndex + 3);
    }



    private void AddQuad(VertexHelper vh, Vector2 posMin, Vector2 posMax, Color color, Vector2 uvMin, Vector2 uvMax)
    {
        int startIndex = vh.currentVertCount;

        vh.AddVert(new Vector3(posMin.x, posMin.y, 0), color, new Vector2(uvMin.x, uvMin.y));
        vh.AddVert(new Vector3(posMin.x, posMax.y, 0), color, new Vector2(uvMin.x, uvMax.y));
        vh.AddVert(new Vector3(posMax.x, posMax.y, 0), color, new Vector2(uvMax.x, uvMax.y));
        vh.AddVert(new Vector3(posMax.x, posMin.y, 0), color, new Vector2(uvMax.x, uvMin.y));

        vh.AddTriangle(startIndex, startIndex + 1, startIndex + 2);
        vh.AddTriangle(startIndex + 2, startIndex + 3, startIndex);
    }

    private void GenerateRoundedRectangle(VertexHelper vh, RectBounds bounds, float cornerRadius, Color color, Vector2 uvMin, Vector2 uvMax)
    {
        int segments = Mathf.Max(4, Mathf.RoundToInt(cornerRadius * 0.5f));

        List<Vector2> vertices = new List<Vector2>();
        List<Vector2> uvs = new List<Vector2>();

        // Center point
        vertices.Add(bounds.center);
        uvs.Add((uvMin + uvMax) * 0.5f);

        // Generate corner vertices - fix the corner positioning and angles
        Vector2[] cornerCenters = {
            new Vector2(bounds.left + cornerRadius, bounds.bottom + cornerRadius), // Bottom-left
            new Vector2(bounds.right - cornerRadius, bounds.bottom + cornerRadius), // Bottom-right
            new Vector2(bounds.right - cornerRadius, bounds.top - cornerRadius), // Top-right
            new Vector2(bounds.left + cornerRadius, bounds.top - cornerRadius)  // Top-left
        };

        // Correct starting angles for each corner (in radians)
        float[] startAngles = {
            Mathf.PI,        // Bottom-left: start from left (180°)
            3f * Mathf.PI / 2f, // Bottom-right: start from bottom (270°)
            0f,              // Top-right: start from right (0°)
            Mathf.PI / 2f    // Top-left: start from top (90°)
        };

        // Add corner arc vertices
        for (int corner = 0; corner < 4; corner++)
        {
            float startAngle = startAngles[corner];
            for (int i = 0; i <= segments; i++)
            {
                float angle = startAngle + (i * Mathf.PI * 0.5f / segments);
                Vector2 offset = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * cornerRadius;
                Vector2 pos = cornerCenters[corner] + offset;

                vertices.Add(pos);

                // Calculate UV
                Vector2 uv = new Vector2(
                    Mathf.Lerp(uvMin.x, uvMax.x, (pos.x - bounds.left) / bounds.width),
                    Mathf.Lerp(uvMin.y, uvMax.y, (pos.y - bounds.bottom) / bounds.height)
                );
                uvs.Add(uv);
            }
        }

        // Add vertices to mesh
        int startIndex = vh.currentVertCount;
        for (int i = 0; i < vertices.Count; i++)
        {
            vh.AddVert(new Vector3(vertices[i].x, vertices[i].y, 0), color, uvs[i]);
        }

        // Add triangles - create a fan from center to all perimeter vertices
        int totalPerimeterVertices = vertices.Count - 1; // Exclude center vertex
        for (int i = 1; i < totalPerimeterVertices; i++)
        {
            int current = startIndex + i;
            int next = startIndex + i + 1;
            vh.AddTriangle(startIndex, current, next);
        }

        // Close the shape by connecting last vertex back to first perimeter vertex
        vh.AddTriangle(startIndex, startIndex + totalPerimeterVertices, startIndex + 1);
    }

    private void GenerateRectangleStroke(VertexHelper vh, RectBounds outerBounds, RectBounds innerBounds, Color strokeColor)
    {
        // Top
        AddQuad(vh, new Vector2(outerBounds.left, innerBounds.top), new Vector2(outerBounds.right, outerBounds.top), strokeColor, Vector2.zero, Vector2.one);
        // Bottom
        AddQuad(vh, new Vector2(outerBounds.left, outerBounds.bottom), new Vector2(outerBounds.right, innerBounds.bottom), strokeColor, Vector2.zero, Vector2.one);
        // Left
        AddQuad(vh, new Vector2(outerBounds.left, innerBounds.bottom), new Vector2(innerBounds.left, innerBounds.top), strokeColor, Vector2.zero, Vector2.one);
        // Right
        AddQuad(vh, new Vector2(innerBounds.right, innerBounds.bottom), new Vector2(outerBounds.right, innerBounds.top), strokeColor, Vector2.zero, Vector2.one);
    }

    private List<Vector2> GenerateRoundedRectangleVertices(RectBounds bounds, float cornerRadius, int segments)
    {
        List<Vector2> vertices = new List<Vector2>();

        // Clamp corner radius to prevent invalid geometry
        float maxRadius = Mathf.Min(bounds.width, bounds.height) * 0.5f;
        cornerRadius = Mathf.Clamp(cornerRadius, 0f, maxRadius);

        // Corner centers
        Vector2[] cornerCenters = {
            new Vector2(bounds.left + cornerRadius, bounds.bottom + cornerRadius), // Bottom-left
            new Vector2(bounds.right - cornerRadius, bounds.bottom + cornerRadius), // Bottom-right
            new Vector2(bounds.right - cornerRadius, bounds.top - cornerRadius), // Top-right
            new Vector2(bounds.left + cornerRadius, bounds.top - cornerRadius)  // Top-left
        };

        // Starting angles for each corner
        float[] startAngles = {
            Mathf.PI,        // Bottom-left: start from left (180°)
            3f * Mathf.PI / 2f, // Bottom-right: start from bottom (270°)
            0f,              // Top-right: start from right (0°)
            Mathf.PI / 2f    // Top-left: start from top (90°)
        };

        // Generate vertices for each corner
        for (int corner = 0; corner < 4; corner++)
        {
            float startAngle = startAngles[corner];

            // Generate arc vertices
            for (int i = 0; i <= segments; i++)
            {
                float angle = startAngle + (i * Mathf.PI * 0.5f / segments);
                Vector2 offset = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * cornerRadius;
                vertices.Add(cornerCenters[corner] + offset);
            }
        }
        return vertices;
    }


}

public enum StrokeLocation
{
    Center,
    Inside,
    Outside
}

[System.Serializable]
public struct RectBounds
{
    public float left;
    public float bottom;
    public float right;
    public float top;

    public RectBounds(float left, float bottom, float right, float top)
    {
        this.left = left;
        this.bottom = bottom;
        this.right = right;
        this.top = top;
    }

    public RectBounds(Rect rect)
    {
        this.left = rect.x;
        this.bottom = rect.y;
        this.right = rect.x + rect.width;
        this.top = rect.y + rect.height;
    }

    public float width => right - left;
    public float height => top - bottom;
    public Vector2 center => new Vector2((left + right) * 0.5f, (bottom + top) * 0.5f);
    public Vector2 bottomLeft => new Vector2(left, bottom);
    public Vector2 bottomRight => new Vector2(right, bottom);
    public Vector2 topLeft => new Vector2(left, top);
    public Vector2 topRight => new Vector2(right, top);

    public RectBounds Expand(float amount)
    {
        return new RectBounds(left - amount, bottom - amount, right + amount, top + amount);
    }

    public RectBounds Contract(float amount)
    {
        return new RectBounds(left + amount, bottom + amount, right - amount, top - amount);
    }

    public override string ToString()
    {
        return $"RectBounds(left:{left:F1}, bottom:{bottom:F1}, right:{right:F1}, top:{top:F1})";
    }
}