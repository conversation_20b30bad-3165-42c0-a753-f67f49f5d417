using UnityEngine;

/// <summary>
/// Example script showing how to subscribe to MouseInputHandler events
/// </summary>
public class MouseInputExample : MonoBehaviour
{
    [Header("Debug Settings")]
    [SerializeField] private bool enableDebugLogs = true;

    private void OnEnable()
    {
        // Subscribe to mouse input events
        MouseInputHandler.OnMouseEnter += HandleMouseEnter;
        MouseInputHandler.OnMouseLeave += HandleMouseLeave;
        MouseInputHandler.OnMouseClickDown += HandleMouseClickDown;
        MouseInputHandler.OnMouseClickUp += HandleMouseClickUp;
    }

    private void OnDisable()
    {
        // Unsubscribe from mouse input events
        MouseInputHandler.OnMouseEnter -= HandleMouseEnter;
        MouseInputHandler.OnMouseLeave -= HandleMouseLeave;
        MouseInputHandler.OnMouseClickDown -= HandleMouseClickDown;
        MouseInputHandler.OnMouseClickUp -= HandleMouseClickUp;
    }

    private void HandleMouseEnter(GameObject hitObject)
    {
        if (enableDebugLogs)
        {
            Debug.Log($"Mouse entered: {hitObject.name}");
        }

        // Example: Change the color of UI elements when mouse enters
        if (hitObject.TryGetComponent<UnityEngine.UI.Image>(out var image))
        {
            image.color = Color.yellow;
        }
    }

    private void HandleMouseLeave(GameObject hitObject)
    {
        if (enableDebugLogs)
        {
            Debug.Log($"Mouse left: {hitObject.name}");
        }

        // Example: Reset the color of UI elements when mouse leaves
        if (hitObject.TryGetComponent<UnityEngine.UI.Image>(out var image))
        {
            image.color = Color.white;
        }
    }

    private void HandleMouseClickDown(GameObject hitObject)
    {
        if (enableDebugLogs)
        {
            Debug.Log($"Mouse clicked down on: {hitObject.name}");
        }

        // Example: Scale the clicked UI element briefly on mouse down
        if (hitObject.TryGetComponent<RectTransform>(out var rectTransform))
        {
            StartCoroutine(ScaleClickEffect(rectTransform));
        }
    }

    private void HandleMouseClickUp(GameObject hitObject)
    {
        if (enableDebugLogs)
        {
            Debug.Log($"Mouse clicked up on: {hitObject.name}");
        }

        // Example: You could add different behavior for mouse up here
        // For now, we'll just log it
    }

    private System.Collections.IEnumerator ScaleClickEffect(RectTransform rectTransform)
    {
        Vector3 originalScale = rectTransform.localScale;
        Vector3 targetScale = originalScale * 1.1f;

        // Scale up
        float duration = 0.1f;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / duration;
            rectTransform.localScale = Vector3.Lerp(originalScale, targetScale, t);
            yield return null;
        }

        // Scale back down
        elapsed = 0f;
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / duration;
            rectTransform.localScale = Vector3.Lerp(targetScale, originalScale, t);
            yield return null;
        }

        rectTransform.localScale = originalScale;
    }
}
