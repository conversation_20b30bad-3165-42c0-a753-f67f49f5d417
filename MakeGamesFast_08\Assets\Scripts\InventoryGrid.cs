using UnityEngine;
using UnityEngine.UI;
using Sirenix.OdinInspector;
using System.Collections.Generic;

[RequireComponent(typeof(RectTransform))]
public class InventoryGrid : MonoBehaviour
{
    public Vector2Int InventorySize = new Vector2Int(4, 4);
    public RectOffset padding;
    public InventorySlot[,] Slots;

    void Awake()
    {
        InitializeInventorySlots();
    }

    public void InitializeInventorySlots()
    {
        // TODO :Add logic that removes all items from the inventory before initializing it again

        UpdateInventorySize();
        SetupAnchorsAndPivot();

        RectTransform inventoryGridRectTransform = this.transform as RectTransform;
        Vector2 inventoryPosition = inventoryGridRectTransform.position;

        // Initialize the 2D array based on InventorySize
        Slots = new InventorySlot[(int)InventorySize.x, (int)InventorySize.y];

        // Populate the array with empty slots
        for (int x = 0; x < InventorySize.x; x++)
        {
            for (int y = 0; y < InventorySize.y; y++)
            {
                InventorySlot inventorySlot = new InventorySlot.Builder(x, y).WithParent(transform).Build();
                Vector2 offset = new Vector2((InventoryStats.CELL_SIZE + InventoryStats.CELL_GAP) * x, -(InventoryStats.CELL_SIZE + InventoryStats.CELL_GAP) * y);
                inventorySlot.transform.position = inventoryPosition + offset + new Vector2(padding.left, -padding.top);
                Slots[x, y] = inventorySlot;
            }
        }
    }


    private void UpdateInventorySize()
    {
        // Set the size of the inventory based on the number of slots
        RectTransform rectTransform = this.transform as RectTransform;
        float width = InventorySize.x * InventoryStats.CELL_SIZE + (InventorySize.x - 1) * InventoryStats.CELL_GAP + padding.left + padding.right;
        float height = InventorySize.y * InventoryStats.CELL_SIZE + (InventorySize.x - 1) * InventoryStats.CELL_GAP + padding.top + padding.bottom;
        rectTransform.sizeDelta = new Vector2(width, height);
    }

    private void SetupAnchorsAndPivot()
    {
        RectTransform inventoryGridRectTransform = this.transform as RectTransform;
        inventoryGridRectTransform.pivot = new Vector2(0.0f, 1.0f);
        inventoryGridRectTransform.anchorMin = new Vector2(0, 1);
        inventoryGridRectTransform.anchorMax = new Vector2(0, 1);
    }

    [Button]
    public void SetupInventoryInEditor()
    {
        UpdateInventorySize();
        SetupAnchorsAndPivot();
    }

    public Vector2 GetSlotWorldPosition(int x, int y)
    {
        return Slots[x, y].transform.position;
    }

    public InventorySlot GetNearestSlotAtPosition(Vector2 position)
    {
        InventorySlot nearestSlot = null;
        float minDistance = float.MaxValue;

        foreach (InventorySlot slot in Slots)
        {
            Vector2 slotPosition = slot.transform.position;
            float distance = Vector2.Distance(position, slotPosition);

            if (distance < minDistance)
            {
                nearestSlot = slot;
                minDistance = distance;
            }
        }
        return nearestSlot;
    }
    public Vector2 GetNearestSlotPositionAtPosition(Vector2 position)
    {
        InventorySlot nearestSlot = GetNearestSlotAtPosition(position);
        if (nearestSlot == null) return Vector2.zero;
        return nearestSlot.transform.position;
    }

    public bool CanPlaceItemAtPosition(Vector2 position, Item item)
    {
        InventorySlot inventorySlot = GetNearestSlotAtPosition(position);
        if (inventorySlot == null) return false;
        if (inventorySlot.X + item.Size.x - 1 >= InventorySize.x) return false;
        if (inventorySlot.Y + item.Size.y - 1 >= InventorySize.y) return false;
        if (!InventoryPlacementUtility.DoRectTransformsIntersect(item.transform as RectTransform, inventorySlot.transform as RectTransform)) return false;

        for (int x = inventorySlot.X; x < inventorySlot.X + item.Size.x; x++)
        {
            for (int y = inventorySlot.Y; y < inventorySlot.Y + item.Size.y; y++)
            {
                if (Slots[x, y].Item != null) return false;
            }
        }

        return true;
    }
    public bool PlaceItemInInventory(Item item, Vector2 position)
    {
        if (!CanPlaceItemAtPosition(position, item)) return false;

        item.SetInventoryGrid(this);
        InventorySlot inventorySlot = GetNearestSlotAtPosition(position);

        for (int x = inventorySlot.X; x < inventorySlot.X + item.Size.x; x++)
        {
            for (int y = inventorySlot.Y; y < inventorySlot.Y + item.Size.y; y++)
            {
                Slots[x, y].Item = item;
            }
        }
        return true;
    }

    public bool RemoveItemFromInventory(Item item)
    {
        if (item.InventoryGrid != this) return false;

        for (int x = 0; x < InventorySize.x; x++)
        {
            for (int y = 0; y < InventorySize.y; y++)
            {
                if (Slots[x, y].Item == item)
                {
                    Slots[x, y].Item = null;
                }
            }
        }
        return true;
    }
}
