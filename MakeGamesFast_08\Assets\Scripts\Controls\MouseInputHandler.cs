using System;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using System.Collections.Generic;

public class MouseInputHandler : MonoBehaviour
{
    [Header("Input Actions")]
    [SerializeField] private InputActionReference mousePositionAction;
    [SerializeField] private InputActionReference mouseClickAction;

    [Header("Raycast Settings")]
    [SerializeField] private LayerMask uiLayerMask = -1;
    [SerializeField] private Camera uiCamera;

    // Events that other scripts can subscribe to
    public static event Action<GameObject> OnMouseEnter;
    public static event Action<GameObject> OnMouseLeave;
    public static event Action<GameObject> OnMouseClickDown;
    public static event Action<GameObject> OnMouseClickUp;
    public static event Action<Vector2> OnMouseMoved;
    public static bool IsMouseClickDown { get; private set; }

    // Private fields
    private GameObject currentHitObject;
    private GraphicRaycaster graphicRaycaster;
    private EventSystem eventSystem;
    private PointerEventData pointerEventData;

    private void Awake()
    {
        // Find the EventSystem in the scene
        eventSystem = FindFirstObjectByType<EventSystem>();
        if (eventSystem == null)
        {
            Debug.LogError("MouseInputHandler: No EventSystem found in scene. Please add an EventSystem to use UI raycasting.");
            enabled = false;
            return;
        }

        // Find the GraphicRaycaster (usually on the Canvas)
        graphicRaycaster = FindFirstObjectByType<GraphicRaycaster>();
        if (graphicRaycaster == null)
        {
            Debug.LogError("MouseInputHandler: No GraphicRaycaster found in scene. Please add a Canvas with GraphicRaycaster component.");
            enabled = false;
            return;
        }

        // If no UI camera is assigned, try to find one or use the main camera
        if (uiCamera == null)
        {
            Canvas canvas = graphicRaycaster.GetComponent<Canvas>();
            if (canvas != null && canvas.worldCamera != null)
            {
                uiCamera = canvas.worldCamera;
            }
            else
            {
                uiCamera = Camera.main;
            }
        }

        // Initialize pointer event data
        pointerEventData = new PointerEventData(eventSystem);
    }

    private void OnEnable()
    {
        if (mousePositionAction != null)
        {
            mousePositionAction.action.Enable();
            mousePositionAction.action.performed += OnMouseMove;
        }

        if (mouseClickAction != null)
        {
            mouseClickAction.action.Enable();
            mouseClickAction.action.started += OnMouseClickDownCallback;
            mouseClickAction.action.canceled += OnMouseClickUpCallback;
        }
    }

    private void OnDisable()
    {
        if (mousePositionAction != null)
        {
            mousePositionAction.action.performed -= OnMouseMove;
            mousePositionAction.action.Disable();
        }

        if (mouseClickAction != null)
        {
            mouseClickAction.action.started -= OnMouseClickDownCallback;
            mouseClickAction.action.canceled -= OnMouseClickUpCallback;
            mouseClickAction.action.Disable();
        }
    }

    private void OnMouseMove(InputAction.CallbackContext context)
    {
        OnMouseMoved?.Invoke(context.ReadValue<Vector2>());

        if (IsMouseClickDown) return;

        Vector2 mousePosition = context.ReadValue<Vector2>();
        PerformUIRaycast(mousePosition);
    }

    private void OnMouseClickDownCallback(InputAction.CallbackContext context)
    {
        OnMouseClickDown?.Invoke(currentHitObject);
        IsMouseClickDown = true;
        //if (currentHitObject != null)
        //{
        //}
    }

    private void OnMouseClickUpCallback(InputAction.CallbackContext context)
    {
        OnMouseClickUp?.Invoke(currentHitObject);
        IsMouseClickDown = false;
        //if (currentHitObject != null)
        //{
        //}
    }

    private void PerformUIRaycast(Vector2 screenPosition)
    {
        // Set the position for the raycast
        pointerEventData.position = screenPosition;

        // Perform the raycast
        List<RaycastResult> results = new();
        graphicRaycaster.Raycast(pointerEventData, results);

        // Filter results by layer mask if specified
        GameObject hitObject = null;
        foreach (var result in results)
        {
            if (IsInLayerMask(result.gameObject.layer, uiLayerMask))
            {
                hitObject = result.gameObject;
                break; // Take the first (topmost) valid hit
            }
        }

        // Check if the hit object has changed
        if (hitObject != currentHitObject)
        {
            // Trigger OnMouseLeave for the previous object
            if (currentHitObject != null)
            {
                OnMouseLeave?.Invoke(currentHitObject);
            }

            // Update current hit object
            currentHitObject = hitObject;

            // Trigger OnMouseEnter for the new object
            if (currentHitObject != null)
            {
                OnMouseEnter?.Invoke(currentHitObject);
            }
        }
    }

    private bool IsInLayerMask(int layer, LayerMask layerMask)
    {
        return (layerMask.value & (1 << layer)) != 0;
    }

}