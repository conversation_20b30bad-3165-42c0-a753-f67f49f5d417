using UnityEngine;
using UnityEngine.InputSystem;

public class ItemSpawnerHandler : MonoBehaviour
{
    public InputAction mouseRightClickAction = new InputAction("Mouse Right Click", InputActionType.But<PERSON>, "Mouse/Right Button");
    public Vector2Int size = Vector2Int.one;
    public float price = 10;
    public string itemName = "Item";

    private void OnEnable()
    {
        mouseRightClickAction.Enable();
        mouseRightClickAction.performed += SpawnItem;
    }

    private void OnDisable()
    {
        mouseRightClickAction.Disable();
        mouseRightClickAction.performed -= SpawnItem;
    }

    private void SpawnItem(InputAction.CallbackContext context)
    {
        Debug.Log("Spawning Item");
        GameObject itemHolder = GameObject.FindGameObjectWithTag("ItemHolder");
        Vector2 mousePosition = Mouse.current.position.ReadValue();
        Item item = new Item.Builder(itemName, size, price).WithParent(itemHolder.transform).Build();
        item.transform.position = mousePosition;
        Debug.Log("Spawned Item: " + item.name + " at " + mousePosition + " with size " + size + " and price " + price);
    }

}