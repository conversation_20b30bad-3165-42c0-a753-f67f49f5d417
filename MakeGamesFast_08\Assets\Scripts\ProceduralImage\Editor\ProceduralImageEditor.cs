using UnityEngine;
using UnityEditor;
using UnityEditor.UI;

[CustomEditor(typeof(ProceduralImage), true)]
[CanEditMultipleObjects]
public class ProceduralImageEditor : GraphicEditor
{
    public override void OnInspectorGUI()
    {
        ProceduralImage proceduralImage = (ProceduralImage)target;

        EditorGUI.BeginChangeCheck();

        // Shape section
        EditorGUILayout.LabelField("Shape", EditorStyles.boldLabel);
        float newCornerRadius = EditorGUILayout.FloatField("Corner Radius", proceduralImage.cornerRadius);

        EditorGUILayout.Space();


        // Fill and Image section
        EditorGUILayout.LabelField("Fill", EditorStyles.boldLabel);
        Texture2D newImage = (Texture2D)EditorGUILayout.ObjectField("Image", proceduralImage.image, typeof(Texture2D), false);
        Color newColor = EditorGUILayout.ColorField("Fill Color", proceduralImage.color);


        EditorGUILayout.Space();

        // Stroke section
        EditorGUILayout.LabelField("Stroke", EditorStyles.boldLabel);
        StrokeLocation newStrokeLocation = (StrokeLocation)EditorGUILayout.EnumPopup("Stroke Location", proceduralImage.strokeLocation);
        float newStrokeWidth = EditorGUILayout.FloatField("Stroke Width", proceduralImage.strokeWidth);
        Color newStrokeColor = EditorGUILayout.ColorField("Stroke Color", proceduralImage.strokeColor);

        EditorGUILayout.Space();

        // Stroke section
        EditorGUILayout.LabelField("Canvas", EditorStyles.boldLabel);
        bool newRaycastTarget = EditorGUILayout.Toggle("Raycast Target", proceduralImage.raycastTarget);
        bool newMaskableTarget = EditorGUILayout.Toggle("Maskable", proceduralImage.maskable);


        if (EditorGUI.EndChangeCheck())
        {
            Undo.RecordObject(proceduralImage, "Procedural Image Properties");

            proceduralImage.cornerRadius = newCornerRadius;
            proceduralImage.strokeLocation = newStrokeLocation;
            proceduralImage.strokeWidth = newStrokeWidth;
            proceduralImage.strokeColor = newStrokeColor;
            proceduralImage.color = newColor;
            proceduralImage.image = newImage;

            proceduralImage.raycastTarget = newRaycastTarget;
            proceduralImage.maskable = newMaskableTarget;

            EditorUtility.SetDirty(proceduralImage);
        }
    }
}
